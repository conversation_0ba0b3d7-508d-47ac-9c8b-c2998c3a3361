@use '../../styles/shared.scss' as *;

body {
  font-family: 'Poppins', sans-serif;
  color: #333;
  background-color: white !important;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  background-color: white;
}

.button-group {
  display: inline-flex !important;
  flex-direction: row-reverse !important;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: nowrap !important;
  align-items: center;
  white-space: nowrap;
}

.row-view .button-group,
.row-view-table .button-group,
.row-view-nested .button-group,
.nested-group-section .button-group,
.grouped-field-section .button-group {
  display: inline-flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  margin: 0;
  padding: 0;
}

@media (max-width: 768px) {
  .button-group {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .button-group {
    gap: 4px;
  }
}

.action-button, .submit-button {
  background-color: #007bff;
  color: #fff;
  font-weight: bold;
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.action-button:hover, .submit-button:hover {
  background-color: #0069d9;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.multi-field, .group-fields {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
  background-color: white;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  position: relative;
}

/* Responsive group styling */
@media (max-width: 768px) {
  .multi-field, .group-fields {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .multi-field, .group-fields {
    padding: 8px;
    gap: 8px;
  }
}

/* Form Fields - Basic layout only (detailed styling moved to RegularFieldComponent) */

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #444;
}

.add-button, .remove-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #000000;
  transition: color 0.3s ease;
}

.add-button:hover {
  color: #0056b3;
}

.remove-button {
  color: #dc3545;
}

.remove-button:hover {
  color: #c82333;
}

.error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 4px;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }

  .form-field input {
    font-size: 14px;
  }

  .group-fields {
    flex-direction: column;
    gap: 10px;
  }
}
.error-message {
  background-color: #fdd; /* Light red background */
  border: 1px solid #faa; /* Red border */
  color: #a00; /* Dark red text color */
  padding: 10px;
  margin-bottom: 10px; 
  border-radius: 4px; /* Slightly rounded corners */
}
/* Add styles for the popup */
.popup {
  position: fixed; 
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%; 
  max-width: 400px; 
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); 
  z-index: 100; 
}

.popup-content {
  text-align: center;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
}



.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  background-color: white;
}

.form-main-field {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  padding-top: 16px;
  background-color: white;
}

.form-label {
  width: 160px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  padding-left: 24px;
}

.input-button-group {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-grow: 1;
  padding-right: 24px;
}

.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  
  &:focus {
    outline: none;
    border-color: #9e9e9e;
    box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  }
  
  &.invalid-input {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    }
  }
  
  &::placeholder {
    color: #999;
  }
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #283A97;
  border-radius: 4px;
  background-color: white;
  color: #283A97;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 40px;
  min-height: 40px;
  position: relative;
  overflow: hidden;
}

.action-button:hover {
  background-color: #ebedf1;
  border: 0px;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 58, 151, 0.2);
}

/* Responsive button sizing */
@media (max-width: 768px) {
  .action-button {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
  }
}

@media (max-width: 480px) {
  .action-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }
}

@media (min-width: 1200px) {
  .action-button {
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
  }
}

.green-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #0DB14B;
  border-radius: 4px;
  background-color: white;
  color: #0DB14B;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 40px;
  min-height: 40px;
  position: relative;
  overflow: hidden;
}

.green-button:hover {
  background-color: #ebedf1;
  border: 0px;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(13, 177, 75, 0.2);
}

.red-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #EB6F62;
  border-radius: 4px;
  background-color: white;
  color: #EB6F62;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 40px;
  min-height: 40px;
  position: relative;
  overflow: hidden;
}

.red-button:hover {
  background-color: #ebedf1;
  border: 0px;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(235, 111, 98, 0.2);
}

.black-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #4D4D4D;
  border-radius: 4px;
  background-color: white;
  color: #4D4D4D;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 40px;
  min-height: 40px;
  position: relative;
  overflow: hidden;
}

.black-button:hover {
  background-color: #ebedf1;
  border: 0px;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(77, 77, 77, 0.2);
}

/* Perfect icon centering - Absolute positioning method */
.action-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.icon-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
}

.icon {
  width: 100%;
  height: 100%;
  max-width: 24px;
  max-height: 24px;
  object-fit: contain;
  transition: all 0.3s ease;
  display: block;
  margin: 0;
  padding: 0;
}

/* Responsive sizing for all button types */
@media (max-width: 768px) {
  .green-button, .red-button, .black-button {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
  }

  .icon-frame {
    width: 20px;
    height: 20px;
  }

  .icon {
    max-width: 20px;
    max-height: 20px;
  }
}

@media (max-width: 480px) {
  .green-button, .red-button, .black-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }

  .icon-frame {
    width: 18px;
    height: 18px;
  }

  .icon {
    max-width: 18px;
    max-height: 18px;
  }
}

@media (min-width: 1200px) {
  .green-button, .red-button, .black-button {
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
  }

  .icon-frame {
    width: 28px;
    height: 28px;
  }

  .icon {
    max-width: 28px;
    max-height: 28px;
  }
}

.text {
  font-size: 16px;
  font-weight: 400;
  color: #222222;
}

.action-button:disabled {
  background-color: #e0e0e0;
  color: #b0b0b0;
  cursor: not-allowed;
}

/////////////////////////////////////////////////////////
/// 
/// 
/// 
.form-fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* Responsive grid adjustments */
@media (min-width: 768px) {
  .form-fields-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .form-fields-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 767px) {
  .form-fields-grid {
    grid-template-columns: 1fr;
  }
}

.form-fields-grid > *:nth-last-child(1):nth-child(odd) {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .form-fields-grid > *:nth-last-child(1):nth-child(odd) {
    grid-column: span 2;
  }
}


.form-field {
  display: flex;
  flex-direction: column;
}


/* حاوية الحقل المتعدد */
.is-multi .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* الحقل نفسه */
.is-multi .multi-input {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink */
}

/* الأزرار */
.is-multi .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

/* Group buttons styling */
.group-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding: 8px;
  border-top: 1px solid #e0e0e0;
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* Angular Material button overrides for better responsiveness and centering */
.mat-mdc-icon-button {
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.mat-mdc-icon-button .mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

.mat-mdc-raised-button {
  min-width: auto !important;
  padding: 8px 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.mat-mdc-raised-button .mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .multi-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .group-buttons {
    justify-content: center;
    gap: 4px;
  }

  .mat-mdc-icon-button {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .mat-mdc-icon-button .mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }

  .mat-mdc-raised-button {
    padding: 6px 12px !important;
    font-size: 12px !important;
  }

  .mat-mdc-raised-button .mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

@media (max-width: 480px) {
  .mat-mdc-icon-button {
    width: 28px !important;
    height: 28px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .mat-mdc-icon-button .mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }

  .mat-mdc-raised-button {
    padding: 4px 8px !important;
    font-size: 11px !important;
  }

  .mat-mdc-raised-button .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
}

/* Additional icon centering fixes */
.mat-mdc-button .mat-icon,
.mat-mdc-raised-button .mat-icon,
.mat-mdc-icon-button .mat-icon {
  vertical-align: middle !important;
  text-align: center !important;
}

/* Fix Material Icons font rendering issues */
.mat-icon {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 24px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
}

/* Ensure icons are properly sized and centered */
.mat-mdc-icon-button .mat-icon {
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
}

/* Force icon visibility and prevent layout shifts */
.mat-icon.material-icons {
  visibility: visible !important;
  opacity: 1 !important;
  transform: none !important;
}

/* Prevent icon flickering during load */
.mat-icon::before {
  content: '' !important;
}

/* Ensure proper icon loading state */
.mat-icon:not(.mat-icon-inline) {
  min-width: 24px !important;
  min-height: 24px !important;
}

/* Prevent icon content from changing after click */
.mat-icon {
  /* Force consistent rendering */
  will-change: auto !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
}

/* Specific fixes for button icons */
.mat-mdc-icon-button .mat-icon {
  /* Prevent icon shape changes on interaction */
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Force hardware acceleration for smooth rendering */
.mat-mdc-icon-button {
  transform: translateZ(0) !important;
  will-change: transform !important;
}

/* Ensure icons maintain their shape during state changes */
.mat-mdc-icon-button:hover .mat-icon,
.mat-mdc-icon-button:focus .mat-icon,
.mat-mdc-icon-button:active .mat-icon {
  transform: none !important;
  font-size: inherit !important;
  width: inherit !important;
  height: inherit !important;
}

/* Ensure Material Design button content is centered */
.mat-mdc-button-base {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Fix for button ripple effect centering */
.mat-mdc-button .mat-mdc-button-touch-target,
.mat-mdc-raised-button .mat-mdc-button-touch-target,
.mat-mdc-icon-button .mat-mdc-button-touch-target {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* Override any default Material Design margins/padding that might affect centering */
.mat-mdc-icon-button .mdc-icon-button__icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
}



/* Checkbox label alignment fixes */
input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
  width: auto !important;
  flex-shrink: 0;
}

/* Checkbox styling moved to RegularFieldComponent */

/* No Input Indicator Styling */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
  margin-left: 4px;
}

/* Unified background for disabled/readonly fields */
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Hover state for disabled/readonly fields */
input[disabled]:hover,
select[disabled]:hover,
textarea[disabled]:hover,
input[readonly]:hover,
select[readonly]:hover,
textarea[readonly]:hover {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
}

/* Focus state for disabled/readonly fields (should not be focusable but just in case) */
input[disabled]:focus,
select[disabled]:focus,
textarea[disabled]:focus,
input[readonly]:focus,
select[readonly]:focus,
textarea[readonly]:focus {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Responsive no-input indicator */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
    font-weight: normal;
    color: #e74c3c;
    margin-left: 2px;
  }
}

@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
    font-weight: normal;
    color: #e74c3c;
    margin-left: 1px;
  }
}

/* Grouped field section styling */
.grouped-field-section {
  width: 100%;
  margin-bottom: 24px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-sizing: border-box;
}

.grouped-field-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

/* Ensure grouped fields span full width in grid */
@media (min-width: 768px) {
  .form-fields-grid .grouped-field-section {
    grid-column: 1 / -1;
  }
}

/* Responsive grouped field styling */
@media (max-width: 767px) {
  .grouped-field-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .grouped-field-section h3 {
    font-size: 14px;
    margin-bottom: 12px;
  }
}







/* Nested Group Styling */
.nested-group-section {
  width: 100%;
  margin: 16px 0;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-sizing: border-box;
}

.nested-group-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.nested-field {
  background-color: white;
  border: 1px solid #f1f3f4;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.nested-group-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Nested group buttons styling */
.nested-group-buttons {
  display: flex;
  gap: 6px;
  margin-top: 12px;
  padding: 6px;
  border-top: 1px solid #f1f3f4;
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* Visual hierarchy for nested groups */
.grouped-field-section {
  border-left: 4px solid #007bff;
}

.nested-group-section {
  border-left: 4px solid #28a745;
  margin-left: 16px;
}

/* Row View Styling */
.row-view {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.row-view-table-container {
  width: 100%;
  overflow-x: auto;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-sizing: border-box;
}

/* Material table styling */
.mat-mdc-table {
  width: 100%;
  background-color: transparent;
}

.mat-mdc-header-cell {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  padding: 12px 16px;
}

.mat-mdc-cell {
  padding: 12px 16px;
}

/* Material row styling */
.mat-mdc-row {
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

/* Material paginator styling */
.mat-mdc-paginator {
  background-color: transparent;
}

/* Material sort header styling */
.mat-mdc-sort-header {
  font-weight: 500;
}

/* Material table container */
.mat-mdc-table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 16px;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
  .row-view-table-container {
    padding: 12px;
  }

  .mat-mdc-header-cell,
  .mat-mdc-cell {
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .row-view-table-container {
    padding: 8px;
  }

  .mat-mdc-header-cell,
  .mat-mdc-cell {
    padding: 6px 8px;
  }
}

/* Material toolbar styling */
.mat-mdc-toolbar {
  background-color: transparent;
  padding: 0;
  margin-bottom: 16px;
}

/* Material button group styling */
.mat-mdc-button-toggle-group {
  margin-bottom: 16px;
}

/* Material chip styling */
.mat-mdc-chip {
  margin: 4px;
}

/* Material progress bar styling */
.mat-mdc-progress-bar {
  margin: 16px 0;
}

/* Material snackbar styling */
.mat-mdc-snack-bar-container {
  &.success-snackbar {
    --mdc-snackbar-container-color: #4caf50;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }

  &.error-snackbar {
    --mdc-snackbar-container-color: #f44336;
    --mat-mdc-snack-bar-button-color: #fff;
    --mdc-snackbar-supporting-text-color: #fff;
  }
}

/* Row View Styling */
.row-view {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 10px;
  margin-bottom: 10px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: none;
}

.row-view-fields {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
}

.row-view-nested {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  box-shadow: none;
}

.row-view-nested-field {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  box-shadow: none;
}

.row-view-nested-fields {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
}

.row-view .form-field,
.row-view-fields .form-field,
.row-view-nested-fields .form-field,
.row-view-nested-fields-seamless .form-field {
  margin-bottom: 0;
  margin-right: 10px;
  min-width: 150px;
}

.row-view-hidden {
  display: none;
}

.row-view .group-buttons,
.row-view-nested .nested-group-buttons,
.row-view-nested-seamless .nested-group-buttons {
  display: flex;
  flex-direction: row;
  gap: 5px;
  margin: 0 0 0 10px;
  padding: 0;
  border: none;
  background-color: transparent;
}

/* Enhanced Row View for inline nested groups */
.row-view .group-fields,
.row-view .nested-group-fields,
.row-view-nested-fields-seamless {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
}

.row-view .nested-group-section,
.row-view-nested-seamless {
  margin: 0;
  padding: 0;
  border: none;
  box-shadow: none;
  background-color: transparent;
}

/* Visual separator between parent and nested fields in row view - REMOVED for seamless appearance */
.row-view .nested-group-section::before {
  content: '';
  display: none;
}

/* Seamless nested group styling - no visual distinction from parent fields */
.row-view-nested-seamless {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  box-shadow: none;
}

.row-view-nested-field-seamless {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  box-shadow: none;
}

.row-view-nested-fields-seamless {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
}

/* Ensure nested fields are properly aligned with parent fields */
.row-view .nested-group-fields .form-field,
.row-view-nested-fields-seamless .form-field {
  display: inline-flex;
  flex-direction: column;
  margin-right: 10px;
}

/* Ensure proper spacing between nested group instances - seamless appearance */
.row-view .nested-group-section + .nested-group-section,
.row-view-nested-seamless + .row-view-nested-seamless {
  margin-left: 0; /* Remove spacing for seamless appearance */
}

/* Improve label visibility in row view */
.row-view .form-field label,
.row-view-nested-fields .form-field label,
.row-view-nested-fields-seamless .form-field label {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  color: #000000;
}

/* Table-like Row View Styling for Groups and Subgroups */
.row-view-table {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 16px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.row-view-table-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.row-view-table-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.row-view-table-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.row-view-table-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #ffffff;
}

.row-view-table-row:last-child {
  border-bottom: none;
}

.row-view-table-cell {
  flex: 1;
  min-width: 150px;
  padding: 0 8px;
}

.row-view-table-cell label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 4px;
}

.row-view-table-cell input,
.row-view-table-cell select {
  width: 100%;
  padding: 6px 8px;
  font-size: 13px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
}

.row-view-table-cell input:focus,
.row-view-table-cell select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* Nested Table Styling */
.row-view-table-nested {
  margin-left: 24px;
  border-left: 2px solid #e0e0e0;
}

.row-view-table-nested .row-view-table-header {
  background-color: #f1f3f4;
}

.row-view-table-nested .row-view-table-row {
  background-color: #fafafa;
}

/* Multi-field in Table */
.row-view-table-cell-multi {
  flex: 1;
  min-width: 200px;
}

.row-view-table-multi-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.row-view-table-multi-item:last-child {
  margin-bottom: 0;
}

.row-view-table-multi-input {
  flex: 1;
}

.row-view-table-multi-buttons {
  display: flex;
  gap: 4px;
}

/* Table Actions */
.row-view-table-actions {
  display: flex;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  justify-content: flex-end;
}

/* Responsive Table Styling */
@media (max-width: 768px) {
  .row-view-table {
    margin-bottom: 12px;
  }

  .row-view-table-header {
    padding: 10px 12px;
  }

  .row-view-table-row {
    padding: 10px 12px;
  }

  .row-view-table-cell {
    min-width: 120px;
    padding: 0 6px;
  }

  .row-view-table-cell-multi {
    min-width: 180px;
  }

  .row-view-table-nested {
    margin-left: 16px;
  }
}

@media (max-width: 480px) {
  .row-view-table {
    margin-bottom: 8px;
  }

  .row-view-table-header {
    padding: 8px 10px;
  }

  .row-view-table-row {
    padding: 8px 10px;
  }

  .row-view-table-cell {
    min-width: 100px;
    padding: 0 4px;
  }

  .row-view-table-cell-multi {
    min-width: 150px;
  }

  .row-view-table-nested {
    margin-left: 12px;
  }
}

/* Table-like Row View Styling */
.row-view-table {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0;
  margin-bottom: 10px;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.row-view-table-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 15px;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: visible;
  padding: 15px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  min-height: 80px;
  width: 100%;
  box-sizing: border-box;

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  /* Ensure smooth scrolling */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  /* Visual indicator for scrollable content */
  position: relative;

  &::after {
    content: '→';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.1);
    color: #666;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}

.row-view-table-cell {
  display: flex;
  flex-direction: column;
  min-width: 150px;
  max-width: 200px;
  flex-shrink: 0;
  margin-right: 0;
}

.row-view-table-cell label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row-view-table-cell input,
.row-view-table-cell select {
  width: 100%;
  padding: 6px 8px;
  font-size: 13px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
}

.row-view-table-cell input:focus,
.row-view-table-cell select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.row-view-table-actions {
  display: flex;
  flex-direction: row;
  gap: 5px;
  margin-left: auto;
  flex-shrink: 0;
  align-items: center;
}

/* Multi-field styling in row view */
.row-view-table-cell-multi {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  max-width: 250px;
  flex-shrink: 0;
  margin-right: 0;
}

.row-view-table-cell-multi label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row-view-multi-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 3px;
}

.row-view-multi-input {
  flex: 1;
  min-width: 0;
}

.row-view-multi-input input,
.row-view-multi-input select {
  width: 100%;
  padding: 4px 6px;
  font-size: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
}

.row-view-multi-input input:focus,
.row-view-multi-input select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.row-view-multi-buttons {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.row-view-multi-buttons .mat-mdc-icon-button {
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
}

.row-view-multi-buttons .mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* Responsive row view styling */
@media (max-width: 1200px) {
  .row-view {
    overflow-x: auto;
  }

  .row-view-fields,
  .row-view-nested-fields,
  .row-view-nested-fields-seamless {
    min-width: max-content;
  }

  .row-view-table-container {
    gap: 10px;
    padding: 12px;
    min-height: 70px;
  }

  .row-view-table-cell {
    min-width: 120px;
    max-width: 150px;
  }

  .row-view-table-cell-multi {
    min-width: 150px;
    max-width: 200px;
  }
}

@media (max-width: 768px) {
  .row-view-table-container {
    gap: 8px;
    padding: 10px;
    min-height: 60px;
  }

  .row-view-table-cell {
    min-width: 100px;
    max-width: 120px;
  }

  .row-view-table-cell-multi {
    min-width: 120px;
    max-width: 150px;
  }

  .row-view-table-cell label {
    font-size: 11px;
  }

  .row-view-table-cell input,
  .row-view-table-cell select {
    padding: 4px 6px;
    font-size: 12px;
  }

  .row-view-multi-input input,
  .row-view-multi-input select {
    padding: 3px 5px;
    font-size: 11px;
  }

  .row-view-multi-buttons .mat-mdc-icon-button {
    width: 20px !important;
    height: 20px !important;
  }

  .row-view-multi-buttons .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
}

/* Responsive nested group styling */
@media (max-width: 767px) {
  .nested-group-section {
    padding: 8px;
    margin: 12px 0;
    margin-left: 8px;
  }

  .nested-group-section h4 {
    font-size: 13px;
    margin-bottom: 8px;
  }

  .nested-field {
    padding: 8px;
    margin-bottom: 6px;
  }

  .nested-group-fields {
    gap: 8px;
  }

  .nested-group-buttons {
    gap: 4px;
    margin-top: 8px;
    padding: 4px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .nested-group-section {
    padding: 6px;
    margin: 8px 0;
    margin-left: 4px;
  }

  .nested-group-section h4 {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .nested-field {
    padding: 6px;
    margin-bottom: 4px;
  }

  .nested-group-fields {
    gap: 6px;
  }

  .nested-group-buttons {
    gap: 2px;
    margin-top: 6px;
    padding: 2px;
  }
}

/* Enhanced visual distinction between group levels */
.grouped-field-section > .form-grid {
  background-color: white;
}

.nested-group-section .nested-field {
  background-color: white;
}

/* Improved spacing for nested multi-fields */
.nested-group-section .form-field h5 {
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
  margin-top: 0;
}

/* Ensure proper alignment of nested group elements */
.nested-group-section .form-field {
  margin-bottom: 12px;
}

.nested-group-section .form-field:last-child {
  margin-bottom: 0;
}

/* Nested group field label styling */
.nested-group-section .form-field label {
  font-size: 13px;
  color: #000000;
  font-weight: 500;
}

/* Nested group input styling */
.nested-group-section .form-field input,
.nested-group-section .form-field select {
  border-color: #e9ecef;
  background-color: white;
}

.nested-group-section .form-field input:focus,
.nested-group-section .form-field select:focus {
  border-color: #9e9e9e;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* Multi-field styling in nested groups */
.nested-group-section .form-field.is-multi {
  margin-bottom: 16px;
}

.nested-group-section .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.nested-group-section .multi-input {
  flex: 1;
  min-width: 0;
}

.nested-group-section .multi-buttons {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.nested-group-section .multi-input input,
.nested-group-section .multi-input select {
  width: 100%;
}

/* Responsive multi-field styling for nested groups */
@media (max-width: 767px) {
  .nested-group-section .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .nested-group-section .multi-buttons {
    justify-content: center;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .nested-group-section .multi-input-container {
    gap: 4px;
  }

  .nested-group-section .multi-buttons {
    gap: 4px;
  }
}
/* Enhanced nested group button styling */
.nested-group-buttons {
  background-color: white;
  border-radius: 4px;
  padding: 8px;
}

.nested-group-buttons button {
  margin: 0 2px;
}

/* Visual distinction for nested multi-fields */
.nested-group-section .form-field.is-multi label {
  color: #495057;
  font-weight: 600;
  font-size: 13px;
}

.nested-group-section .form-field.is-multi .multi-input-container {
  background-color: white;
  border: 1px solid #f1f3f4;
  border-radius: 4px;
  padding: 8px;
}

.row-view-table.vertical-stack {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.row-view-table.vertical-stack .row-view-table-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.row-view-table.vertical-stack .row-view-table-cell {
  width: 100%;
  margin-bottom: 10px;
}

.row-view-table.vertical-stack .row-view-table-cell-multi {
  width: 100%;
  margin-bottom: 15px;
}

.row-view-table.vertical-stack .row-view-table-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.groups-vertical-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.groups-vertical-container .row-view-table {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 0;
  background-color: white;
}

.groups-vertical-container .row-view-table-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.groups-vertical-container .row-view-table-cell {
  flex: 1;
  min-width: 200px;
}

.groups-vertical-container .row-view-table-cell-multi {
  flex: 1;
  min-width: 200px;
}

.groups-vertical-container .row-view-table-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

/* Material form field styling */
.mat-mdc-form-field {
  width: 100%;
  margin-bottom: 8px;
}

/* Material input styling */
.mat-mdc-input-element {
  width: 100%;
  box-sizing: border-box;
}

/* Material button styling */
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 0 16px;
  border-radius: 4px;
}

/* Material icon styling */
.mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }

  .mat-mdc-form-field {
    margin-bottom: 4px;
  }

  .mat-mdc-button,
  .mat-mdc-raised-button,
  .mat-mdc-icon-button {
    min-width: 36px;
    height: 36px;
    padding: 0 12px;
  }

  .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    line-height: 18px;
  }
}

@media (max-width: 480px) {
  .form-grid {
    padding: 8px;
  }

  .mat-mdc-button,
  .mat-mdc-raised-button,
  .mat-mdc-icon-button {
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
  }

  .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    line-height: 16px;
  }
}

/* Material card styling for groups */
.mat-mdc-card {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.mat-mdc-card-content {
  padding: 16px;
}

/* Material expansion panel styling */
.mat-mdc-expansion-panel {
  width: 100%;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.mat-mdc-expansion-panel-header {
  height: 48px;
  padding: 0 16px;
}

/* Material divider styling */
.mat-mdc-divider {
  margin: 16px 0;
}

/* Material select styling */
.mat-mdc-select {
  width: 100%;
}

.mat-mdc-select-panel {
  max-height: 300px;
}

/* Material autocomplete styling */
.mat-mdc-autocomplete-panel {
  max-height: 300px;
}

/* Material multi-field styling */
.multi-field {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.multi-input {
  flex: 1;
  min-width: 0;
}

.multi-buttons {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

/* Material chip list styling */
.mat-mdc-chip-listbox {
  width: 100%;
  margin-bottom: 16px;
}

/* Material chip input styling */
.mat-mdc-chip-input {
  flex: 1;
  min-width: 0;
}

/* Material chip row styling */
.mat-mdc-chip-row {
  min-height: 32px;
  padding: 0 8px;
}

/* Responsive multi-field adjustments */
@media (max-width: 768px) {
  .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .multi-buttons {
    justify-content: flex-end;
  }

  .mat-mdc-chip-row {
    min-height: 28px;
    padding: 0 6px;
  }
}

@media (max-width: 480px) {
  .multi-input-container {
    gap: 6px;
  }

  .multi-buttons {
    gap: 2px;
  }

  .mat-mdc-chip-row {
    min-height: 24px;
    padding: 0 4px;
  }
}

/* Material form field hints */
.mat-mdc-form-field-hint {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

/* Material form field suffix/prefix */
.mat-mdc-form-field-suffix,
.mat-mdc-form-field-prefix {
  color: rgba(0, 0, 0, 0.54);
}

/* Material form field appearance */
.mat-mdc-form-field-appearance-outline {
  .mat-mdc-form-field-infix {
    padding: 8px 0;
  }
}
/* Material form field focus state */
.mat-mdc-form-field.mat-focused {
  .mat-mdc-form-field-flex {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.validation-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}





/* Toggle View Button - Teal theme */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #00695C 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 150, 136, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/* Submit Button - Green theme */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Validate Button - Blue theme */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }
}

/* Authorize Button - Purple theme */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/* Back Button - Gray theme */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(96, 125, 139, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/* Reject Button - Red theme */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }
}

/* Delete Button - Dark Red theme */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #A00000 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* Responsive sizing for form action buttons */
@media (max-width: 768px) {
  .form-action-button {
    padding: 6px 12px;
    font-size: 13px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .form-action-button {
    padding: 5px 10px;
    font-size: 12px;
    gap: 6px;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }
}

.form-grid {
  display: grid;
  gap: 16px;

  &.columns-1 {
    grid-template-columns: 1fr;
  }

  &.columns-2 {
    grid-template-columns: 1fr 1fr;
  }

  &.columns-3 {
    grid-template-columns: 1fr 1fr 1fr;
  }

  &.columns-4 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }

  &.columns-5 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  }

  &.columns-6 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  }

  &.columns-7 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  }

  &.columns-8 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  }

  &.columns-9 {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  }

  // Add more columns if needed
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
::placeholder {
  color: #999 !important;
  opacity: 1 !important;
}

/* Required field indicator styling for .form-field moved to RegularFieldComponent */

/* Main form label required indicator */
.form-label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* Row view table cell label required indicator */
.row-view-table-cell label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* Form-field label span styling moved to RegularFieldComponent */

/* Nested group multi-field label styling */
.nested-group-section .form-field.is-multi label {
  color: #000000;
  font-weight: 600;
  font-size: 13px;
}



