import { Component, Input, Output, EventEmitter, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MetadataService } from '../services/metadata.service';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { environment } from '../../environments/environment';



@Component({
  selector: 'app-dynamic-screen',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './dynamic-screen.component.html',
  styleUrl: './dynamic-screen.component.scss'
})
export class DynamicScreenComponent implements OnInit {

  @Input() screenName!: string;
  form!: FormGroup;
  fields: any[] = [];
  submissionSuccess = false;
  errorMessage = '';
  isLoading = false;
  showInitialInput = true;
  isViewMode = false;
  tableName!: string; // New variable to store tableName without extra characters
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();

  private metadataService = inject(MetadataService);
  private fb = inject(FormBuilder);
  private http = inject(HttpClient);

  // Centralized API configuration using environment baseURL
  private readonly endpoints = {
    validation: `${environment.baseURL}/api/validation/validate-id`,
    tables: `${environment.baseURL}/api/tables`
  } as const;

  private readonly httpOptions = {
    withCredentials: true
  } as const;

  constructor(){}
  ngOnInit() {
    if (this.screenName) {
      this.tableName = this.screenName.split(',')[0];
      
      this.initializeForm();
    }
  }

  initializeForm() {
    this.form = this.fb.group({
      ID: ['', Validators.required]
    });
    
  }

  viewData() {
    this.isViewMode = true;
    this.loadDataAndBuildForm();
  }

  loadDataAndBuildForm() {
    this.isLoading = true;
    this.errorMessage = '';
    const id = this.form.get('ID')?.value;
    const apiUrl = `${this.endpoints.validation}?tableName=${this.tableName}&id=${id}`;

    this.http.get(apiUrl, this.httpOptions).subscribe(
      (response: any) => {
        if (response.success) {
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          this.errorMessage = response.message || 'ID validation failed';
          this.isLoading = false;
        }
      },
      (error) => {
        this.errorMessage = 'Error validating ID';
        console.error('Error:', error);
        this.isLoading = false;
      }
    );
  }

  loadTableMetadata() {
    this.metadataService.getScreenMetadata(this.screenName).subscribe(
      (response: any) => {
        if (response?.data?.fieldName) {
          this.fields = response.data.fieldName;
          this.buildForm();
          this.fetchFormData();
        } else {
          console.error('Invalid metadata response:', response);
          this.isLoading = false;
        }
      },
      (error) => {
        console.error('Error fetching metadata:', error);
        this.isLoading = false;
      }
    );
  }

  buildForm() {
    const groupedFields: { [key: string]: FormArray } = {};

    this.fields.forEach((field) => {
      if (field.fieldName !== 'ID') {
        if (field.isMulti) {
          this.form.addControl(field.fieldName, this.fb.array([this.createMultiField(field)]));
        } else if (field.Group) {
          if (!groupedFields[field.Group]) {
            groupedFields[field.Group] = this.fb.array([]);
            this.form.addControl(field.Group, groupedFields[field.Group]);
            this.addGroup(field.Group);
          }
        } else {
          if (field.type === 'boolean') {
            this.form.addControl(
              field.fieldName,
              this.fb.control(false, field.mandatory ? Validators.required : null)
            );
          } else {
            this.form.addControl(
              field.fieldName,
              this.fb.control('', field.mandatory ? Validators.required : null)
            );
          }
        }
      }
    });
  }

  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});
    this.getFieldsForGroup(groupName).forEach((field) => {
      group.addControl(
        field.fieldName,
        this.fb.control('', field.mandatory ? Validators.required : null)
      );
    });
    return group;
  }

  createMultiField(field: any): FormGroup {
    return this.fb.group({
      [field.fieldName]: this.fb.control('', field.mandatory ? Validators.required : null)
    });
  }

  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group === groupName);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }
  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);
  
    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup); // Insert after the specified index
    } else {
      groupArray.push(newGroup); // Default behavior, add to the end
    }
  }
  

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  getMultiArray(fieldName: string): FormArray {
    return this.form.get(fieldName) as FormArray;
  }

  addMultiField(field: any, index?: number) {
    const multiArray = this.getMultiArray(field.fieldName);
    const newField = this.createMultiField(field);
  
    if (index !== undefined) {
      multiArray.insert(index + 1, newField); // Insert after the specified index
    } else {
      multiArray.push(newField); // Default behavior, add to the end
    }
  }
  

  removeMultiField(fieldName: string, index: number) {
    this.getMultiArray(fieldName).removeAt(index);
  }

  isFirstFieldInGroup(field: any): boolean {
    return this.fields.findIndex(f => f.Group === field.Group) === this.fields.indexOf(field);
  }

  trackByFieldName(index: number, field: any): string {
    return field.fieldName;
  }

  authorizeRecord() {
    const id = this.form.get('ID')?.value;
    const apiUrl = `${this.endpoints.tables}/${this.tableName}/records/${id}/authorize`;

    this.http.put(apiUrl, {}, this.httpOptions).subscribe(
      (response: any) => {
        if (response && response.status === 'success') {
          this.errorMessage = '';
          this.submissionSuccess = true;
        } else {
          this.errorMessage = response.message || 'Authorization failed';
        }
      },
      (error) => {
        this.errorMessage = 'An error occurred during authorization';
        console.error('Authorization Error:', error);
      }
    );
  }

  onSubmit() {
    if (this.form.valid) {
      const apiUrl = `${this.endpoints.tables}/${this.tableName}/records`;
      const formData = this.buildFormData(this.form.value);

      this.http.post(apiUrl, formData, this.httpOptions).subscribe(
        (response: any) => {
          if (response.status === 'success') {
            this.submissionSuccess = true;
          } else if (response.status === 'error') {
            this.errorMessage = response.message || 'An error occurred while submitting the form';
          }
        },
        (error) => {
          this.errorMessage = 'An error occurred while submitting the form';
          console.error('Error submitting form:', error);
        }
      );
    } else {
      console.error('Form is invalid');
    }
  }

  private buildFormData(data: any): any {
    const result: { [key: string]: any } = {};
  
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        // Skip fields that are empty (null, undefined, or empty string)
        const value = data[key];
        if (value === null || value === undefined || value === '') continue;
  
        if (Array.isArray(value)) {
          if (this.fields.find(field => field.fieldName === key && field.isMulti)) {
            // For multi fields, flatten the values
            result[key] = value.map((item: any) => item[key]).filter((v: any) => v !== null && v !== undefined && v !== '');
          } else {
            // Recursively handle nested arrays/objects
            const nestedData = value.map((item: any) => this.buildFormData(item)).filter((obj: any) => Object.keys(obj).length > 0);
            if (nestedData.length > 0) {
              result[key] = nestedData;
            }
          }
        } else if (typeof value === 'object') {
          // For nested objects, recursively build data and exclude empty objects
          const nestedObject = this.buildFormData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[key] = nestedObject;
          }
        } else {
          result[key] = value;
        }
      }
    }
  
    return result;
  }
  

  fetchFormData() {
    this.isLoading = true;
    const id = this.form.get('ID')?.value;
    const apiUrl = `${this.endpoints.tables}/${this.tableName}/records/${id}`;

    this.http.get(apiUrl, this.httpOptions).subscribe(
      (response: any) => {
        if (response && response.data) {
          this.populateForm(response.data);
          this.submissionSuccess = false;
        } else {
          this.buildForm();
          this.showInitialInput = false;
          this.errorMessage = 'No data found for the provided ID, opening an empty form.';
        }
        this.isLoading = false;
      },
      (error) => {
        this.errorMessage = 'An error occurred while fetching data';
        console.error('Error fetching data:', error);
        this.isLoading = false;
      }
    );
  }

  populateForm(data: any) {
    Object.keys(data).forEach(key => {
      const formControl = this.form.get(key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear();

        if (this.fields.some(field => field.Group === key)) {
          data[key].forEach((groupData: any) => {
            formArray.push(this.createGroup(key));
            (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
          });
        } else {
          data[key].forEach((value: any) => {
            formArray.push(this.createMultiField({ fieldName: key, mandatory: true }));
            formArray.at(formArray.length - 1).patchValue({ [key]: value });
          });
        }
      } else if (formControl) {
        formControl.setValue(data[key]);

        if (this.isViewMode) {
          formControl.disable();
        }
      }
    });
  }

  goBack() {
    this.showInitialInput = true;
    this.form.reset();
    this.initializeForm();
    this.errorMessage = '';
    this.fields = [];
    this.isViewMode = false;
  }

  groupFieldsByRow() {
    const groupedFields: { [key: number]: any[] } = {};
    this.fields.forEach((field) => {
      const row = field.row;
      if (!groupedFields[row]) {
        groupedFields[row] = [];
      }
      groupedFields[row].push(field);
    });
    return groupedFields;
  }
}
