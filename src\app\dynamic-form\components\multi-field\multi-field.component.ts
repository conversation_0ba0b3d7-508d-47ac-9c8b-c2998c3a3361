import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormArray, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

@Component({
  selector: 'app-multi-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    RegularFieldComponent
  ],
  templateUrl: './multi-field.component.html',
  styleUrls: ['./multi-field.component.scss']
})
export class MultiFieldComponent implements OnInit {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = [];
  @Input() groupIndex?: number;
  @Input() groupName?: string;
  @Input() nestedGroupIndex?: number;
  @Input() isRowView?: boolean = false;
  @Input() showLabel?: boolean = true;
  @Input() labelText?: string;

  @Output() fieldValueChange = new EventEmitter<any>();
  @Output() multiFieldAdded = new EventEmitter<any>();
  @Output() multiFieldRemoved = new EventEmitter<any>();

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    // Component initialization logic
  }

  /**
   * Create a new multi-field FormGroup
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Parse group path to get parent and child group names
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Get group array from form
   */
  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get multi-field array with support for grouped and nested fields
   */
  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  /**
   * Add a new multi-field instance
   */
  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number): void {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

      // Emit event for parent component
      this.multiFieldAdded.emit({
        field,
        groupIndex,
        index,
        groupName,
        nestedGroupIndex
      });
    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Remove a multi-field instance
   */
  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): void {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);

    // Emit event for parent component
    this.multiFieldRemoved.emit({
      fieldName,
      index,
      groupIndex,
      groupName,
      nestedGroupIndex
    });
  }

  /**
   * Handle field value changes and emit to parent
   */
  onFieldValueChange(event: any): void {
    this.fieldValueChange.emit(event);
  }
}
