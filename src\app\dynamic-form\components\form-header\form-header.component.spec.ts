import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { FormHeaderComponent } from './form-header.component';

describe('FormHeaderComponent', () => {
  let component: FormHeaderComponent;
  let fixture: ComponentFixture<FormHeaderComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        FormHeaderComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [FormBuilder]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FormHeaderComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);
    
    // Setup required inputs
    component.form = formBuilder.group({
      ID: ['TEST123']
    });
    component.isViewMode = false;
    component.isRowView = false;
    component.errorMessage = '';
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit toggleViewMode when toggle view button is clicked', () => {
    spyOn(component.toggleViewMode, 'emit');
    component.onToggleViewMode();
    expect(component.toggleViewMode.emit).toHaveBeenCalled();
  });

  it('should emit submitForm when submit button is clicked', () => {
    spyOn(component.submitForm, 'emit');
    component.onSubmitForm();
    expect(component.submitForm.emit).toHaveBeenCalled();
  });

  it('should emit validateRecord when validate button is clicked', () => {
    spyOn(component.validateRecord, 'emit');
    component.onValidateRecord();
    expect(component.validateRecord.emit).toHaveBeenCalled();
  });

  it('should emit authorizeRecord when authorize button is clicked', () => {
    spyOn(component.authorizeRecord, 'emit');
    component.onAuthorizeRecord();
    expect(component.authorizeRecord.emit).toHaveBeenCalled();
  });

  it('should emit goBack when back button is clicked', () => {
    spyOn(component.goBack, 'emit');
    component.onGoBack();
    expect(component.goBack.emit).toHaveBeenCalled();
  });

  it('should emit rejectRecord when reject button is clicked', () => {
    spyOn(component.rejectRecord, 'emit');
    component.onRejectRecord();
    expect(component.rejectRecord.emit).toHaveBeenCalled();
  });

  it('should emit deleteRecord when delete button is clicked', () => {
    spyOn(component.deleteRecord, 'emit');
    component.onDeleteRecord();
    expect(component.deleteRecord.emit).toHaveBeenCalled();
  });

  it('should display the form ID value', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('p')?.textContent).toContain('TEST123');
  });

  it('should show correct view mode text based on isRowView', () => {
    component.isRowView = false;
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.toggle-view-button')?.textContent?.trim()).toContain('Row View');

    component.isRowView = true;
    fixture.detectChanges();
    expect(compiled.querySelector('.toggle-view-button')?.textContent?.trim()).toContain('Nested View');
  });
});
