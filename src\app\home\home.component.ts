import { Component, OnInit,
  ViewChild,
  ViewContainerRef,
  ComponentRef,
  ChangeDetectorRef,
  EnvironmentInjector,
  ElementRef,
  AfterViewInit,
  inject } from '@angular/core';
import { MatSidenavModule, MatSidenav } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { MetadataService } from '../services/metadata.service';
import { AuthenticationService } from '../services/authentication.service';
import { NavigationService } from '../services/navigation.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SubmenuComponent } from '../submenu/submenu.component';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';
import { DynamicFormComponent } from '../dynamic-form/dynamic-form.component';
import { DynamicScreenComponent } from '../dynamic-screen/dynamic-screen.component';
import { DynamicQueryComponent } from '../dynamic-query/dynamic-query.component';

@Component({
  selector: 'app-home',
  imports: [MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    SubmenuComponent,
    MatSidenav],
     animations: [
    trigger('tabAnimation', [
      transition(':increment', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateX(20px)' }),
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
          ])
        ], { optional: true })
      ]),
      transition(':decrement', [
        query(':leave', [
          stagger(50, [
            animate('300ms ease-out', style({ opacity: 0, transform: 'translateX(-20px)' }))
          ])
        ], { optional: true })
      ])
    ])
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit, AfterViewInit {
  tabs: any[] = [];
  sidebarMenus: { [key: string]: any[] } = {};
  tabMenus: { [key: string]: any[] } = {};
  loadingSidebarMenus: { [key: string]: boolean } = {};
  loadingTabMenus: { [key: string]: boolean } = {};
  expandedSidebarMenus: { [key: string]: boolean } = {};
  expandedTabMenus: { [key: string]: { [key: string]: boolean } } = {};

  openTabs: any[] = [];
  activeTabIndex: number = -1;

  @ViewChild('sidenav') sidenav!: MatSidenav;
  isSidenavOpen: boolean = false;

  @ViewChild('tabContent', { read: ViewContainerRef }) tabContent!: ViewContainerRef;
  @ViewChild('tabScrollWrapper') tabScrollWrapper!: ElementRef;
  @ViewChild('tabsList') tabsList!: ElementRef;

  // Tab scrolling properties
  canScrollLeft: boolean = false;
  canScrollRight: boolean = false;


   userPrivileges: any[] = [];

  componentRefs: { [key: number]: ComponentRef<any> } = {}; // Store component references
  private environmentInjector = inject(EnvironmentInjector);

constructor(private metadataService: MetadataService,
    private authenticationService: AuthenticationService,
    private navigationService: NavigationService,
    private router: Router,
    private location: Location,
    private changeDetectorRef: ChangeDetectorRef
){}
//  ngOnInit() {
//     const profile = JSON.parse(localStorage.getItem('profile') || '{}');
//     this.tabs = profile.menus || [];
//     console.log('Main Menu Loaded:', this.tabs);

//     this.location.subscribe(() => {
//       this.onLogout();
//     });
//   }

ngOnInit() {
  const profile = JSON.parse(localStorage.getItem('profile') || '{}');
  this.tabs = profile.menus || [];
  console.log('Main Menu Loaded:', this.tabs);

  const privilegesRaw = localStorage.getItem('userPrivileges');
  if (privilegesRaw) {
    try {
      const parsed = JSON.parse(privilegesRaw);
      this.userPrivileges = parsed;
    } catch (e) {
      console.error('Failed to parse userPrivileges:', e);
      this.userPrivileges = [];
    }
  }

  this.location.subscribe(() => {
    this.onLogout();
  });
}

selectBranch(branch: any): void {
  localStorage.setItem('selectedBranch', JSON.stringify(branch));
  console.log('Branch selected:', branch);
  window.location.reload();
}

  ngAfterViewInit() {
    // Check scroll state after view initialization
    setTimeout(() => {
      this.checkScrollState();
    }, 100);

    // Add resize observer to update scroll state when container size changes
    if (this.tabScrollWrapper) {
      const resizeObserver = new ResizeObserver(() => {
        this.checkScrollState();
      });
      resizeObserver.observe(this.tabScrollWrapper.nativeElement);
    }
  }

   onSidebarMenuItemSelected(menuItem: any) {
    if (menuItem.type === 'menu') {
      this.expandedSidebarMenus[menuItem.application] = !this.expandedSidebarMenus[menuItem.application];
      if (!this.sidebarMenus[menuItem.application]) {
        this.loadSidebarSubmenu(menuItem.application);
      }
    } else {
      this.openTab(menuItem);
    }
  
    // Reinitialize or reorganize layout
    setTimeout(() => {
      this.changeDetectorRef.detectChanges();
    }, 100); // Delay of 100ms
  }
  

  openTab(menuItem: any) {
    let existingTabIndex = -1;
    const isQuery = menuItem.type === 'qur';

    if (isQuery) {
      existingTabIndex = this.openTabs.findIndex(t => t.type === 'qur');
    } else {
      existingTabIndex = this.openTabs.findIndex(
        (tab) => tab.application === menuItem.application && tab.type === menuItem.type
      );
    }

    if (existingTabIndex > -1) {
      // Tab exists
      this.activeTabIndex = existingTabIndex;
      const existingTab = this.openTabs[existingTabIndex];

      if (isQuery && existingTab.application !== menuItem.application) {
        // It's a different query, so we need to update the component
        // Destroy the old component
        if (this.componentRefs[existingTab.id]) {
          this.componentRefs[existingTab.id].destroy();
          delete this.componentRefs[existingTab.id];
        }
        // Update the tab data
        this.openTabs[existingTabIndex] = { ...menuItem, id: existingTab.id, data: {}, formState: null };
      }
    } else {
      // Create a new tab
      const newTab = { ...menuItem, id: Date.now(), data: {}, formState: null };
      this.openTabs.push(newTab);
      this.activeTabIndex = this.openTabs.length - 1;
    }

    // Load component for the active tab.
    // This will create it if it doesn't exist.
    if (this.activeTabIndex !== -1) {
      this.loadComponent(this.openTabs[this.activeTabIndex]);
    }

    // Check scroll state after tab changes
    setTimeout(() => {
      this.checkScrollState();
    }, 100);
  }
  
  loadComponent(tab: any) {
    // Ensure all other components are hidden before proceeding
    this.hideAllComponents();

    if (this.componentRefs[tab.id]) {
      // If the component already exists, show it
      this.showComponent(tab.id);
    } else {
      // Create the component and store its reference
      this.createComponent(tab);
    }
  
    // Trigger change detection to ensure the view updates
    this.changeDetectorRef.detectChanges();
  }

  // Helper function to extract API ID from application string
  private extractApiId(application: string): string {
    if (application && application.includes(',')) {
      return application.split(',')[0].trim();
    }
    return application;
  }

  createComponent(tab: any) {
    let componentRef: ComponentRef<any>;
    
    // Both 'table' and 'scr' types now use DynamicFormComponent
    switch (tab.type) {
      case 'table':
        componentRef = this.tabContent.createComponent(DynamicFormComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.tableName = tab.application;
        break;
      case 'scr':
        componentRef = this.tabContent.createComponent(DynamicFormComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.screenName = tab.application; // Use screenName for screen types
        break;
      case 'qur':
        componentRef = this.tabContent.createComponent(DynamicQueryComponent, {
          environmentInjector: this.environmentInjector
        });
        componentRef.instance.queryName = tab.application;
        break;
      default:
        console.error('Unknown tab type:', tab.type);
        return;
    }

    // Store the component reference and initialize data
    this.componentRefs[tab.id] = componentRef;
    componentRef.instance.data = tab.data;

    // Listen for data changes to keep the state updated
    componentRef.instance.dataChange.subscribe((updatedData: any) => {
      tab.data = updatedData;
    });

    // Hide all other components and show the new one
    this.showComponent(tab.id);
  }


  hideAllComponents() {
    for (const key in this.componentRefs) {
      if (this.componentRefs.hasOwnProperty(key)) {
        this.componentRefs[key].location.nativeElement.style.display = 'none';
      }
    }
  }

  showComponent(tabId: number) {
    // First, hide all components
    this.hideAllComponents();
  
    // Then, show the active one
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].location.nativeElement.style.display = 'block';
    }
  }
  

  setActiveTab(index: number) {
    this.activeTabIndex = index;
    this.showComponent(this.openTabs[index].id);
  }
  

  closeTab(index: number) {
    const tabId = this.openTabs[index].id;
    this.openTabs.splice(index, 1);

    // Destroy the component and remove its reference
    if (this.componentRefs[tabId]) {
      this.componentRefs[tabId].destroy();
      delete this.componentRefs[tabId];
    }

    if (this.openTabs.length === 0) {
      this.activeTabIndex = -1;
    } else if (this.activeTabIndex >= this.openTabs.length) {
      this.activeTabIndex = this.openTabs.length - 1;
    }

    if (this.activeTabIndex !== -1) {
      this.showComponent(this.openTabs[this.activeTabIndex].id);
    }

    // Check scroll state after tab removal
    setTimeout(() => {
      this.checkScrollState();
    }, 100);
  }

  loadSidebarSubmenu(application: string) {
    this.loadingSidebarMenus[application] = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(application);

    this.metadataService.getMenu(apiId).subscribe({
      next: (response: any) => {
        this.sidebarMenus[application] = response?.menus || [];
        this.loadingSidebarMenus[application] = false;
      },
      error: (error: any) => {
        console.error(`Error fetching sidebar submenu for ${apiId}:`, error);
        this.loadingSidebarMenus[application] = false;
      }
    });
  }

  loadTabSubmenu(application: string) {
    this.loadingTabMenus[application] = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(application);

    this.metadataService.getMenu(apiId).subscribe({
      next: (response: any) => {
        this.tabMenus[application] = response?.menus || [];
        this.loadingTabMenus[application] = false;
      },
      error: (error: any) => {
        console.error(`Error fetching tab submenu for ${apiId}:`, error);
        this.loadingTabMenus[application] = false;
      }
    });
  }

  toggleSideMenu() {
    this.sidenav.toggle();
    this.isSidenavOpen = !this.isSidenavOpen;
  }

  getIcon(menuType: string): string {
    switch (menuType) {
      case 'menu':
        return 'list';
      case 'qur':
        return 'query_stats';
      case 'scr':
        return 'screen_share';
      case 'table':
        return 'table_chart';
      default:
        return 'info';
    }
  }

  // Tab scrolling methods
  checkScrollState() {
    if (this.tabScrollWrapper && this.tabsList) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const list = this.tabsList.nativeElement;

      this.canScrollLeft = wrapper.scrollLeft > 0;
      this.canScrollRight = wrapper.scrollLeft < (list.scrollWidth - wrapper.clientWidth);

      console.log('Scroll state check:', {
        scrollLeft: wrapper.scrollLeft,
        scrollWidth: list.scrollWidth,
        clientWidth: wrapper.clientWidth,
        canScrollLeft: this.canScrollLeft,
        canScrollRight: this.canScrollRight
      });

      // Force change detection to update button states
      this.changeDetectorRef.detectChanges();
    } else {
      console.log('Elements not ready for scroll state check');
    }
  }

  scrollTabsLeft() {
    console.log('Scroll left clicked');
    if (this.tabScrollWrapper) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const scrollAmount = 150;

      console.log('Current scroll position:', wrapper.scrollLeft);
      console.log('Scrolling to:', wrapper.scrollLeft - scrollAmount);

      wrapper.scrollTo({
        left: wrapper.scrollLeft - scrollAmount,
        behavior: 'smooth'
      });

      setTimeout(() => {
        this.checkScrollState();
      }, 300);
    } else {
      console.log('tabScrollWrapper not found');
    }
  }

  scrollTabsRight() {
    console.log('Scroll right clicked');
    if (this.tabScrollWrapper) {
      const wrapper = this.tabScrollWrapper.nativeElement;
      const scrollAmount = 150;

      console.log('Current scroll position:', wrapper.scrollLeft);
      console.log('Scrolling to:', wrapper.scrollLeft + scrollAmount);

      wrapper.scrollTo({
        left: wrapper.scrollLeft + scrollAmount,
        behavior: 'smooth'
      });

      setTimeout(() => {
        this.checkScrollState();
      }, 300);
    } else {
      console.log('tabScrollWrapper not found');
    }
  }

  // Add scroll event listener to update arrow states
  onTabScroll() {
    this.checkScrollState();
  }

  onLogout() {
    this.authenticationService.logout().subscribe({
      next: () => {
        localStorage.clear();
        sessionStorage.clear();
        this.navigationService.navigateToLogin();
      },
      error: (error: any) => {
        console.error('Logout failed:', error);
      }
    });
  }

}
