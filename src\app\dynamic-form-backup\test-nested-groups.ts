// Test data for nested groups functionality
// This demonstrates how the userRole response should be structured

export const userRoleTestData = {
  "data": {
    "ID": "userRole",
    "fieldName": [
      // Parent group "type" fields
      {
        "fieldName": "type",
        "type": "string",
        "mandatory": true,
        "Group": "type"
      },
      {
        "fieldName": "applicationName",
        "type": "string",
        "mandatory": true,
        "Group": "type"
      },
      {
        "fieldName": "function",
        "type": "string",
        "isMulti": true,
        "Group": "type"
      },
      
      // Nested group "type|field" fields - these should appear together in the "field" subgroup
      {
        "fieldName": "field",
        "type": "string",
        "Group": "type|field"
      },
      {
        "fieldName": "allowedData",
        "type": "string",
        "mandatory": false,
        "Group": "type|field"
      },
      {
        "fieldName": "fieldFunction",
        "type": "string",
        "isMulti": true,
        "Group": "type|field"
      },
      
      // Non-grouped fields
      {
        "fieldName": "ID",
        "type": "string",
        "noInput": false
      },
      {
        "fieldName": "recordStatus",
        "type": "string",
        "isMulti": false,
        "noInput": true
      },
      {
        "fieldName": "tenantCode",
        "type": "string",
        "noInput": true
      },
      {
        "fieldName": "recordInputter",
        "type": "string",
        "isMulti": false,
        "noInput": true
      },
      {
        "fieldName": "recordAuthorizer",
        "type": "string",
        "isMulti": true,
        "noInput": true
      },
      {
        "fieldName": "recordCount",
        "type": "string",
        "noInput": true
      },
      {
        "fieldName": "dateTime",
        "type": "string",
        "isMulti": false,
        "noInput": false
      }
    ],
    "recordInputter": "OSAMA",
    "recordAuthorizer": "OSAMA",
    "recordCount": 3,
    "dateTime": "2025-03-18 11:17:00"
  },
  "status": "success"
};

// Expected form structure after processing:
/*
Form Structure:
├── ID (non-grouped field)
├── type (FormArray) - Parent Group
│   ├── [0] (FormGroup) - First instance of type group
│   │   ├── type (FormControl)
│   │   ├── applicationName (FormControl)
│   │   ├── function (FormArray) - Multi-field
│   │   │   ├── [0] (FormGroup)
│   │   │   │   └── function (FormControl)
│   │   │   └── [1] (FormGroup)
│   │   │       └── function (FormControl)
│   │   └── field (FormArray) - Nested subgroup
│   │       ├── [0] (FormGroup) - First instance of field subgroup
│   │       │   ├── field (FormControl)
│   │       │   ├── allowedData (FormControl)
│   │       │   └── fieldFunction (FormArray) - Multi-field in nested group
│   │       │       ├── [0] (FormGroup)
│   │       │       │   └── fieldFunction (FormControl)
│   │       │       └── [1] (FormGroup)
│   │       │           └── fieldFunction (FormControl)
│   │       └── [1] (FormGroup) - Second instance of field subgroup
│   │           ├── field (FormControl)
│   │           ├── allowedData (FormControl)
│   │           └── fieldFunction (FormArray)
│   │               └── [0] (FormGroup)
│   │                   └── fieldFunction (FormControl)
│   └── [1] (FormGroup) - Second instance of type group
│       ├── type (FormControl)
│       ├── applicationName (FormControl)
│       ├── function (FormArray)
│       │   └── [0] (FormGroup)
│       │       └── function (FormControl)
│       └── field (FormArray)
│           └── [0] (FormGroup)
│               ├── field (FormControl)
│               ├── allowedData (FormControl)
│               └── fieldFunction (FormArray)
│                   └── [0] (FormGroup)
│                       └── fieldFunction (FormControl)
├── recordStatus (non-grouped field, read-only)
├── tenantCode (non-grouped field, read-only)
├── recordInputter (non-grouped field, read-only)
├── recordAuthorizer (FormArray, read-only, multi-field)
├── recordCount (non-grouped field, read-only)
└── dateTime (non-grouped field)
*/

// Test helper functions
export class NestedGroupTestHelper {
  
  /**
   * Test if fields are correctly grouped
   */
  static testFieldGrouping(fields: any[]) {
    const typeFields = fields.filter(f => f.Group === 'type');
    const nestedFields = fields.filter(f => f.Group === 'type|field');
    
    console.log('Type group fields:', typeFields.map(f => f.fieldName));
    console.log('Nested field group fields:', nestedFields.map(f => f.fieldName));
    
    // Expected type group fields
    const expectedTypeFields = ['type', 'applicationName', 'function'];
    const actualTypeFields = typeFields.map(f => f.fieldName);
    
    // Expected nested field group fields
    const expectedNestedFields = ['field', 'allowedData', 'fieldFunction'];
    const actualNestedFields = nestedFields.map(f => f.fieldName);
    
    console.log('Type fields match:', 
      expectedTypeFields.every(f => actualTypeFields.includes(f)));
    console.log('Nested fields match:', 
      expectedNestedFields.every(f => actualNestedFields.includes(f)));
  }
  
  /**
   * Test parsing of group paths
   */
  static testGroupPathParsing() {
    const parseGroupPath = (groupPath: string) => {
      if (groupPath.includes('|')) {
        const parts = groupPath.split('|');
        return {
          parent: parts[0].trim(),
          child: parts[1].trim(),
          isNested: true
        };
      }
      return {
        parent: groupPath,
        child: null,
        isNested: false
      };
    };
    
    const typeGroup = parseGroupPath('type');
    const nestedGroup = parseGroupPath('type|field');
    
    console.log('Type group parsing:', typeGroup);
    console.log('Nested group parsing:', nestedGroup);
    
    console.log('Type group is not nested:', !typeGroup.isNested);
    console.log('Nested group is nested:', nestedGroup.isNested);
    console.log('Nested group parent is "type":', nestedGroup.parent === 'type');
    console.log('Nested group child is "field":', nestedGroup.child === 'field');
  }
}
