<!-- Multi Field Component Template -->
<div [formArrayName]="field.fieldName" class="multi-field-container">
  @for (control of getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex).controls; track control; let j = $index) {
    <div [formGroupName]="j" class="form-field is-multi">
      @if (field.foreginKey) {
        <!-- Foreign key multi-field using regular-field component -->
        <app-regular-field
          [field]="field"
          [form]="$any(control)"
          [isViewMode]="isViewMode"
          [fields]="fields"
          [multiIndex]="j + 1"
          (fieldValueChange)="onFieldValueChange($event)">
        </app-regular-field>
      } @else {
        <!-- Non-foreign key multi-field with direct input controls -->
        <label>{{ field.fieldName }} ({{ j + 1 }})
          @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
        </label>

        <div class="multi-input-container">
          <div class="multi-input">
            <!-- Regular input fields for non-foreign key multi-fields -->
            @if (field.type === 'string') {
              <input [formControlName]="field.fieldName" type="text"
                [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)"
                [disabled]="isViewMode || field.noInput" />
            }
            @if (field.type === 'int') {
              <input [formControlName]="field.fieldName" type="number"
                [disabled]="isViewMode || field.noInput" />
            }
            @if (field.type === 'boolean') {
              <input [formControlName]="field.fieldName" type="checkbox"
                [disabled]="isViewMode || field.noInput" />
            }
            @if (field.type === 'date') {
              <input [formControlName]="field.fieldName" type="date"
                [disabled]="isViewMode || field.noInput" />
            }
            @if (field.type === 'double') {
              <input [formControlName]="field.fieldName" type="number"
                step="00.50" [disabled]="isViewMode || field.noInput" />
            }
          </div>

          <div class="multi-buttons">
            @if (getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex).length > 1 && !isViewMode && !field.noInput) {
              <button mat-icon-button color="warn" type="button"
                (click)="removeMultiField(field.fieldName, j, groupIndex, groupName, nestedGroupIndex)"
                matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            }

            @if (!isViewMode && !field.noInput) {
              <button mat-icon-button color="primary" type="button"
                (click)="addMultiField(field, groupIndex, j, groupName, nestedGroupIndex)"
                matTooltip="Add">
                <mat-icon>add</mat-icon>
              </button>
            }
          </div>
        </div>
      }
    </div>
  }
</div>
