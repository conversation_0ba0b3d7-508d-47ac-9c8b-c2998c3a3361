/* EXACT styles from main dynamic-form.component.scss lines 102-160, 309-330, 902-950, 1214-1400 */

/* Form Fields - EXACT from main component lines 102-160 */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

.form-field input,
.form-field select {
  width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

/* Responsive input sizing - EXACT from main component lines 129-143 */
@media (max-width: 768px) {
  .form-field input,
  .form-field select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .form-field input,
  .form-field select {
    padding: 8px 10px;
    font-size: 14px;
  }
}

.form-field input:focus,
.form-field select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* Form Input - EXACT from main component lines 309-330 */
.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
}

/* Override form-input background when disabled/readonly */
.form-input[disabled],
.form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

.form-input:focus {
  outline: none;
  border-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
}

.form-input.invalid-input {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

/* Dynamic Dropdown Styles - EXACT from main component lines 1214-1400 */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.dropdown-input-container .form-input {
  flex-grow: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
  padding: 8px 12px;
}

.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* Disabled state for dropdown arrow buttons */
.dropdown-input-container .dropdown-arrow-btn:disabled {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 0.6 !important;
}

.dropdown-input-container .dropdown-arrow-btn:disabled:hover {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  transform: none !important;
}

/* Dropdown List Styles - EXACT from main component lines 1263-1302 */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/* Checkbox styling - EXACT from main component lines 902-943 */
.form-field:has(input[type="checkbox"]) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

.form-field:has(input[type="checkbox"]) label {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  order: 2;
}

.form-field:has(input[type="checkbox"]) input[type="checkbox"] {
  order: 1;
  margin-right: 8px;
}

/* Required field indicator styling - EXACT from main component line 3064-3068 */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No Input Indicator Styling - EXACT from main component lines 946-950 */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
}

/* Unified background for disabled/readonly fields */
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Higher specificity rules to override .form-field and .form-input styles */
.form-field input[disabled],
.form-field select[disabled],
.form-field textarea[disabled],
.form-field input[readonly],
.form-field select[readonly],
.form-field textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Dropdown input container specific rules */
.dropdown-input-container .form-input[disabled],
.dropdown-input-container input[disabled],
.dropdown-input-container .form-input[readonly],
.dropdown-input-container input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Force styling for dropdown inputs with specific class combinations */
.dropdown-input-container .form-input.dropdown-input,
.dropdown-input-container input.dropdown-input {
  &[disabled], &[readonly] {
    background-color: #f1f3f4 !important;
    color: #5f6368 !important;
    cursor: not-allowed !important;
    border-color: #dadce0 !important;
    opacity: 1 !important;
    pointer-events: none !important;
  }
}

/* Additional high-specificity rules for dropdown inputs with multiple classes */
.dropdown-input-container .form-input.dropdown-input[disabled],
.dropdown-input-container input.dropdown-input[disabled],
.dropdown-input-container .form-input.dropdown-input[readonly],
.dropdown-input-container input.dropdown-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Override any focus states for disabled dropdown inputs */
.dropdown-input-container .form-input[disabled]:focus,
.dropdown-input-container input[disabled]:focus,
.dropdown-input-container .form-input.dropdown-input[disabled]:focus,
.dropdown-input-container input.dropdown-input[disabled]:focus {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Additional protection for any potential edge cases with specific field types */
input[type="text"][disabled]:focus,
input[type="number"][disabled]:focus,
input[type="date"][disabled]:focus,
input[type="checkbox"][disabled]:focus,
input[type="text"][readonly]:focus,
input[type="number"][readonly]:focus,
input[type="date"][readonly]:focus,
input[type="checkbox"][readonly]:focus {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Ensure no user interaction is possible on disabled fields */
input[disabled],
select[disabled],
textarea[disabled] {
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Specific input type coverage for complete consistency */
input[type="text"][disabled],
input[type="number"][disabled],
input[type="date"][disabled],
input[type="checkbox"][disabled],
input[type="text"][readonly],
input[type="number"][readonly],
input[type="date"][readonly],
input[type="checkbox"][readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Higher specificity for form field input types */
.form-field input[type="text"][disabled],
.form-field input[type="number"][disabled],
.form-field input[type="date"][disabled],
.form-field input[type="checkbox"][disabled],
.form-field input[type="text"][readonly],
.form-field input[type="number"][readonly],
.form-field input[type="date"][readonly],
.form-field input[type="checkbox"][readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Hover state for disabled/readonly fields */
input[disabled]:hover,
select[disabled]:hover,
textarea[disabled]:hover,
input[readonly]:hover,
select[readonly]:hover,
textarea[readonly]:hover,
.form-field input[disabled]:hover,
.form-field select[disabled]:hover,
.form-field textarea[disabled]:hover,
.form-field input[readonly]:hover,
.form-field select[readonly]:hover,
.form-field textarea[readonly]:hover,
.dropdown-input-container .form-input[disabled]:hover,
.dropdown-input-container input[disabled]:hover,
.dropdown-input-container .form-input[readonly]:hover,
.dropdown-input-container input[readonly]:hover {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
}

/* Focus state for disabled/readonly fields */
input[disabled]:focus,
select[disabled]:focus,
textarea[disabled]:focus,
input[readonly]:focus,
select[readonly]:focus,
textarea[readonly]:focus,
.form-field input[disabled]:focus,
.form-field select[disabled]:focus,
.form-field textarea[disabled]:focus,
.form-field input[readonly]:focus,
.form-field select[readonly]:focus,
.form-field textarea[readonly]:focus,
.dropdown-input-container .form-input[disabled]:focus,
.dropdown-input-container input[disabled]:focus,
.dropdown-input-container .form-input[readonly]:focus,
.dropdown-input-container input[readonly]:focus {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}



/* Responsive no-input indicator - EXACT from backup lines 993-999 */
@media (max-width: 768px) {
  .no-input-indicator {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .no-input-indicator {
    font-size: 9px;
  }
}

/* Responsive dropdown styling - EXACT from main component lines 1305-1349 */
@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }

  /* Responsive checkbox alignment - EXACT from main component lines 923-933 */
  .form-field:has(input[type="checkbox"]) {
    flex-direction: row !important;
    align-items: center !important;
    gap: 6px !important;
  }

  input[type="checkbox"] {
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }

  /* Responsive checkbox alignment - EXACT from main component lines 935-943 */
  .form-field:has(input[type="checkbox"]) {
    gap: 4px !important;
  }

  input[type="checkbox"] {
    margin-right: 4px;
  }
}

/* Scrollbar styling for dropdown lists - EXACT from main component lines 1352-1368 */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading state for dropdown lists - EXACT from main component lines 1371-1378 */
.dropdown-list.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

/* Ensure dropdown containers work well in multi-field and grouped contexts - EXACT from main component lines 1381-1396 */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}

/* Ensure dropdowns don't overflow in small containers */
.form-field .dropdown-input-container {
  max-width: 100%;
  overflow: visible;
}

/* Checkbox label alignment fixes - EXACT from backup */
input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
  width: auto !important;
  flex-shrink: 0;
}

.form-field:has(input[type="checkbox"]) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

.form-field:has(input[type="checkbox"]) label {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  order: 2;
}

.form-field:has(input[type="checkbox"]) input[type="checkbox"] {
  order: 1;
  margin-right: 8px;
}

/* Responsive checkbox alignment */
@media (max-width: 768px) {
  .form-field:has(input[type="checkbox"]) {
    flex-direction: row !important;
    align-items: center !important;
    gap: 6px !important;
  }

  input[type="checkbox"] {
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .form-field:has(input[type="checkbox"]) {
    gap: 4px !important;
  }

  input[type="checkbox"] {
    margin-right: 4px;
  }
}
