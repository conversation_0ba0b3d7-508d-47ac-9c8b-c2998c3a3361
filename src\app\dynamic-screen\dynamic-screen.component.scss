body {
  font-family: Arial, sans-serif;
  color: #333;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.button-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.action-button, .submit-button {
  background-color: #007bff;
  color: #fff;
  font-weight: bold;
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.action-button:hover, .submit-button:hover {
  background-color: #0069d9;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.multi-field, .group-fields {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.form-field input {
  width: 160px;
  padding: 6px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  height: 28px;
  transition: border-color 0.3s ease;
}

.form-field input:focus {
  border-color: #f4d808;
  outline: none;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #666;
}

h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #444;
}

.add-button, .remove-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #007bff;
  transition: color 0.3s ease;
}

.add-button:hover {
  color: #0056b3;
}

.remove-button {
  color: #dc3545;
}

.remove-button:hover {
  color: #c82333;
}

.error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 4px;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }

  .form-field input {
    width: 100%;
    font-size: 14px;
  }

  .group-fields {
    flex-direction: column;
    gap: 10px;
  }
}
