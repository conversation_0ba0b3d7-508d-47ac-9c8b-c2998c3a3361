import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { MultiFieldComponent } from './multi-field.component';

describe('MultiFieldComponent', () => {
  let component: MultiFieldComponent;
  let fixture: ComponentFixture<MultiFieldComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MultiFieldComponent,
        ReactiveFormsModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MultiFieldComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);
    
    // Setup basic inputs
    component.field = { fieldName: 'testField', isMulti: true };
    component.form = formBuilder.group({});
    component.fields = [];
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit field value changes', () => {
    spyOn(component.fieldValueChange, 'emit');
    const testEvent = { field: 'test', value: 'testValue' };
    
    component.onFieldValueChange(testEvent);
    
    expect(component.fieldValueChange.emit).toHaveBeenCalledWith(testEvent);
  });
});
