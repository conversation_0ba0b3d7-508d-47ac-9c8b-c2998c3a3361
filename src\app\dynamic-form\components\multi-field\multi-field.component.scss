/* Multi Field Component Styles */

.multi-field-container {
  width: 100%;
}

/* Core multi-field styling */
.multi-field {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  position: relative;
}

/* Multi-field input container */
.is-multi .multi-input-container,
.multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* Multi-field input */
.is-multi .multi-input,
.multi-input {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink */
}

/* Multi-field buttons */
.is-multi .multi-buttons,
.multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

/* Form field styling for multi-fields */
.form-field.is-multi {
  margin-bottom: 16px;
}

/* Material chip list styling */
.mat-mdc-chip-listbox {
  width: 100%;
  margin-bottom: 16px;
}

/* Material chip input styling */
.mat-mdc-chip-input {
  flex: 1;
  min-width: 0;
}

/* Material chip row styling */
.mat-mdc-chip-row {
  min-height: 32px;
  padding: 0 8px;
}

/* Input styling within multi-fields */
.multi-input input,
.multi-input select {
  width: 100%;
}

/* No input indicator styling */
.no-input-indicator {
  color: #6c757d;
  font-style: italic;
  font-size: 0.875em;
}

/* Responsive multi-field adjustments */
@media (max-width: 768px) {
  .multi-field {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }

  .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .multi-buttons {
    justify-content: flex-end;
    flex-direction: column;
    gap: 2px;
  }

  .mat-mdc-chip-row {
    min-height: 28px;
    padding: 0 6px;
  }
}

@media (max-width: 480px) {
  .multi-field {
    padding: 8px;
    gap: 8px;
  }

  .multi-input-container {
    gap: 6px;
  }

  .multi-buttons {
    gap: 2px;
  }

  .mat-mdc-chip-row {
    min-height: 24px;
    padding: 0 4px;
  }
}

/* Row view specific multi-field styling */
.row-view-multi-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 3px;
}

.row-view-multi-input {
  flex: 1;
  min-width: 0;
}

.row-view-multi-input input,
.row-view-multi-input select {
  width: 100%;
  padding: 4px 6px;
  font-size: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
}

.row-view-multi-input input:focus,
.row-view-multi-input select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.row-view-multi-buttons {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.row-view-multi-buttons .mat-mdc-icon-button {
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
}

.row-view-multi-buttons .mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}
