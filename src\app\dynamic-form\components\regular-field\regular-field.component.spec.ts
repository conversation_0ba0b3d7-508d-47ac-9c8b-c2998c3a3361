import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Component } from '@angular/core';
import { RegularFieldComponent } from './regular-field.component';

@Component({
  template: `
    <div [formGroup]="form">
      <app-regular-field
        [form]="form"
        [field]="field"
        [fields]="fields"
        [isViewMode]="isViewMode">
      </app-regular-field>
    </div>
  `
})
class TestWrapperComponent {
  form!: FormGroup;
  field: any;
  fields: any[] = [];
  isViewMode = false;
}

describe('RegularFieldComponent', () => {
  let component: TestWrapperComponent;
  let fixture: ComponentFixture<TestWrapperComponent>;
  let regularFieldComponent: RegularFieldComponent;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestWrapperComponent],
      imports: [
        RegularFieldComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatIconModule,
        MatTooltipModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TestWrapperComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    // Setup basic test data
    component.form = formBuilder.group({
      testField: ['']
    });

    component.field = {
      fieldName: 'testField',
      label: 'Test Field',
      type: 'string',
      mandatory: false,
      noInput: false,
      isMulti: false
    };

    component.fields = [component.field];
    component.isViewMode = false;

    fixture.detectChanges();

    // Get reference to the actual RegularFieldComponent
    regularFieldComponent = fixture.debugElement.query(
      (el) => el.componentInstance instanceof RegularFieldComponent
    )?.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(regularFieldComponent).toBeTruthy();
  });

  it('should render regular field for string type', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('input[type="text"]')).toBeTruthy();
  });

  it('should render checkbox for boolean type', () => {
    component.field.type = 'boolean';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('input[type="checkbox"]')).toBeTruthy();
  });

  it('should render number input for int type', () => {
    component.field.type = 'int';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('input[type="number"]')).toBeTruthy();
  });

  it('should render date input for date type', () => {
    component.field.type = 'date';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('input[type="date"]')).toBeTruthy();
  });

  it('should render dropdown for foreign key fields', () => {
    component.field.foreginKey = 'testForeignKey';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.dropdown-input-container')).toBeTruthy();
    expect(compiled.querySelector('.dropdown-arrow-btn')).toBeTruthy();
  });

  it('should show mandatory indicator when field is mandatory', () => {
    component.field.mandatory = true;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('label span')).toBeTruthy();
  });

  it('should show read-only indicator when field has noInput', () => {
    component.field.noInput = true;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.no-input-indicator')).toBeTruthy();
  });

  it('should disable input in view mode', () => {
    component.isViewMode = true;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const input = compiled.querySelector('input') as HTMLInputElement;
    expect(input.disabled).toBeTruthy();
  });

  it('should disable input when field has noInput', () => {
    component.field.noInput = true;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const input = compiled.querySelector('input') as HTMLInputElement;
    expect(input.disabled).toBeTruthy();
  });

  it('should disable dropdown arrow button in view mode', () => {
    component.field.foreginKey = 'testForeignKey';
    component.isViewMode = true;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const button = compiled.querySelector('.dropdown-arrow-btn') as HTMLButtonElement;
    expect(button?.disabled).toBeTruthy();
  });

  it('should disable dropdown arrow button when field has noInput', () => {
    component.field.foreginKey = 'testForeignKey';
    component.field.noInput = true;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    const button = compiled.querySelector('.dropdown-arrow-btn') as HTMLButtonElement;
    expect(button?.disabled).toBeTruthy();
  });

  it('should emit field value change on dropdown selection', () => {
    spyOn(component.fieldValueChange, 'emit');
    
    const option = { ROW_ID: 'test-value' };
    component.selectTypeOption(option, 'testField');
    
    expect(component.fieldValueChange.emit).toHaveBeenCalledWith({
      fieldName: 'testField',
      value: 'test-value'
    });
  });

  it('should clear timeouts on destroy', () => {
    component.typeSearchTimeout['testField'] = setTimeout(() => {}, 1000);
    component.foreignKeySearchTimeout['testField'] = setTimeout(() => {}, 1000);
    component.regularSearchTimeout['testField'] = setTimeout(() => {}, 1000);
    
    spyOn(window, 'clearTimeout');
    
    component.ngOnDestroy();
    
    expect(clearTimeout).toHaveBeenCalledTimes(3);
  });
});
